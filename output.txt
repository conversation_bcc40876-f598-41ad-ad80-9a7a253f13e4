2025-06-28 09:42:19,917 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-28 09:42:19,918 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-28 09:42:19,918 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-28 09:42:19,919 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-06-28 09:42:19,919 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-06-28 09:42:19,919 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-28 09:42:19,920 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-06-28 09:42:19,920 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-06-28 09:42:19,921 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-06-28 09:42:19,921 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-06-28 09:42:19,921 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-06-28 09:42:19,922 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-06-28 09:42:19,922 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-06-28 09:42:21,983 - __main__ - INFO - Existing processes terminated
2025-06-28 09:42:23,442 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-06-28 09:42:23,490 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-06-28 09:42:24,058 - app - INFO - Using directories from config.py:
2025-06-28 09:42:24,058 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-28 09:42:24,058 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-28 09:42:24,058 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-06-28 09:42:24,062] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-28 09:42:24,063] INFO in database: Test_steps table schema updated successfully
[2025-06-28 09:42:24,063] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 09:42:24,064] INFO in database: Screenshots table schema updated successfully
[2025-06-28 09:42:24,064] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 09:42:24,065] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 09:42:24,065] INFO in database: Database initialized successfully
[2025-06-28 09:42:24,065] INFO in database: Checking initial database state...
[2025-06-28 09:42:24,084] INFO in database: Database state: 0 suites, 0 cases, 8766 steps, 1 screenshots, 0 tracking entries
[2025-06-28 09:42:24,084] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-28 09:42:24,085] INFO in database: Test_steps table schema updated successfully
[2025-06-28 09:42:24,085] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 09:42:24,086] INFO in database: Screenshots table schema updated successfully
[2025-06-28 09:42:24,086] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 09:42:24,086] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 09:42:24,086] INFO in database: Database initialized successfully
[2025-06-28 09:42:24,086] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 09:42:24,087] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 09:42:24,087] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 09:42:24,088] INFO in database: Screenshots table schema updated successfully
[2025-06-28 09:42:24,088] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-06-28 09:42:24,088] INFO in database: Found 0 records in execution_tracking table before clearing
[2025-06-28 09:42:24,090] INFO in database: Successfully cleared execution_tracking table. Removed 0 records.
[2025-06-28 09:42:24,194] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-28 09:42:24,195] INFO in global_values_db: Global values database initialized successfully
[2025-06-28 09:42:24,195] INFO in global_values_db: Using global values from config.py
[2025-06-28 09:42:24,195] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-28 09:42:24,255] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-28 09:42:24,269] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x119c6dd30>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-28 09:42:24,269] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-06-28 09:42:24,301] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-06-28 09:42:24,333] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-06-28 09:42:26,339] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-06-28 09:42:26,339] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-06-28 09:42:27,681] INFO in appium_device_controller: Installed Appium drivers: 
[2025-06-28 09:42:27,682] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-06-28 09:42:28,521] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-06-28 09:42:28,522] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-06-28 09:42:28,522] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-06-28 09:42:28,528] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-06-28 09:42:30,543] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 1/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x119d44910>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-28 09:42:32,560] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:32,560] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:34,567] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:34,567] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:36,601] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:36,601] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:38,609] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:38,609] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:40,613] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:40,613] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:42,620] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:42,621] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:44,627] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:44,627] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:46,633] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:46,633] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:48,641] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:48,641] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:50,647] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:50,647] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:52,655] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:52,656] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:54,664] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:54,664] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:56,672] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:56,672] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:42:58,678] INFO in appium_device_controller: Appium server started successfully
[2025-06-28 09:42:58,678] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-06-28 09:42:58,711] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-06-28 09:42:58,711] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-06-28 09:43:07,264] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-06-28 09:43:07,264] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:07,265] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:12,255] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:12,256] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:14,491] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET / HTTP/1.1" 200 -
[2025-06-28 09:43:14,534] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,535] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,544] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,549] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,549] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,549] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,550] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,554] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,557] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,558] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,561] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,562] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,563] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
[2025-06-28 09:43:14,566] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,567] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,575] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /static/js/action-manager.js?v=1751070794 HTTP/1.1" 200 -
[2025-06-28 09:43:14,578] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,590] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,591] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,593] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,595] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,597] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,599] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,605] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,605] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,607] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /static/js/main.js?v=1751070794 HTTP/1.1" 200 -
[2025-06-28 09:43:14,608] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,623] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,625] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,626] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,628] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,631] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,633] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,711] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-28 09:43:14,717] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-28 09:43:14,718] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:14,720] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/environments HTTP/1.1" 200 -
[2025-06-28 09:43:14,731] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:14,737] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-06-28 09:43:14,741] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-28 09:43:14,742] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/settings HTTP/1.1" 200 -
[2025-06-28 09:43:14,749] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-06-28 09:43:14,753] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-28 09:43:14,808] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-28 09:43:14,810] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/environments/current HTTP/1.1" 200 -
[2025-06-28 09:43:14,845] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-28 09:43:14,858] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-28 09:43:14,868] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-06-28 09:43:14,891] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-06-28 09:43:14,910] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-28 09:43:14,994] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:14] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-28 09:43:15,006] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:15] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-28 09:43:15,023] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:15] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-06-28 09:43:15,036] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:15] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-28 09:43:16,498] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-28 09:43:16,503] INFO in appium_device_controller: Appium server is running and ready
[2025-06-28 09:43:16,503] INFO in appium_device_controller: Appium server is already running and responsive
[2025-06-28 09:43:16,503] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:16] "GET /api/devices HTTP/1.1" 200 -
[2025-06-28 09:43:18,436] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Appium server is running and ready
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Appium server is already running and responsive
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Connecting to device: 00008120-00186C801E13C01E with options: None, platform hint: iOS
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Connection attempt 1/3
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-06-28 09:43:18,441] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-06-28 09:43:18,442] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-06-28 09:43:18,442] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-06-28 09:43:18,451] INFO in appium_device_controller: WebDriverAgent is already running at http://localhost:8100
[2025-06-28 09:43:18,451] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.1', 'time': 'May 27 2025 13:38:32', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': '1578C83E-3B7A-4198-9F80-C9E74D8E258E'}
[2025-06-28 09:43:18,455] INFO in appium_device_controller: Appium server is already running
[2025-06-28 09:43:18,455] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-06-28 09:43:18,455] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-06-28 09:43:18,460] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '8d543f43397758400233064b5d1b51ebb6b2b689', 'built': '2025-06-27 07:54:51 +1000'}}}
[2025-06-28 09:43:18,460] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-06-28 09:43:19,159] INFO in appium_device_controller: Successfully connected to iOS device
[2025-06-28 09:43:19,159] INFO in appium_device_controller: Connected with session ID: 18540f97-a3d4-483b-a6ec-a11408a0aa08
[2025-06-28 09:43:19,159] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-06-28 09:43:19,159] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-06-28 09:43:19,159] INFO in appium_device_controller: Getting device dimensions
[2025-06-28 09:43:19,533] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-06-28 09:43:19,533] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-06-28 09:43:19,539] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-06-28 09:43:19,539] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-06-28 09:43:19,539] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-06-28 09:43:19,540] INFO in appium_device_controller: iOS version: 18.0
[2025-06-28 09:43:19,540] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-06-28 09:43:19,540] INFO in appium_device_controller: Platform helpers initialization completed
[2025-06-28 09:43:19,540] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-06-28 09:43:19,540] INFO in action_factory: Registered basic actions: tap, wait
[2025-06-28 09:43:19,541] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-06-28 09:43:19,542] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-06-28 09:43:19,542] INFO in action_factory: Registered action handler for 'multiStep'
[2025-06-28 09:43:19,543] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-06-28 09:43:19,543] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-06-28 09:43:19,544] INFO in action_factory: Registered action handler for 'swipe'
[2025-06-28 09:43:19,545] INFO in action_factory: Registered action handler for 'getParam'
[2025-06-28 09:43:19,546] INFO in action_factory: Registered action handler for 'wait'
[2025-06-28 09:43:19,546] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-06-28 09:43:19,547] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-06-28 09:43:19,548] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-06-28 09:43:19,548] INFO in action_factory: Registered action handler for 'text'
[2025-06-28 09:43:19,550] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-06-28 09:43:19,551] INFO in action_factory: Registered action handler for 'waitTill'
[2025-06-28 09:43:19,552] INFO in action_factory: Registered action handler for 'hookAction'
[2025-06-28 09:43:19,556] ERROR in action_factory: Error loading action handler from input_text_action: invalid syntax (input_text_action.py, line 226)
[2025-06-28 09:43:19,557] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-28 09:43:19,558] INFO in global_values_db: Global values database initialized successfully
[2025-06-28 09:43:19,558] INFO in global_values_db: Using global values from config.py
[2025-06-28 09:43:19,558] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-28 09:43:19,559] INFO in action_factory: Registered action handler for 'setParam'
[2025-06-28 09:43:19,559] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-06-28 09:43:19,560] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-06-28 09:43:19,560] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-06-28 09:43:19,561] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-06-28 09:43:19,561] INFO in action_factory: Registered action handler for 'clickImage'
[2025-06-28 09:43:19,562] INFO in action_factory: Registered action handler for 'tap'
[2025-06-28 09:43:19,563] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-06-28 09:43:19,564] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-06-28 09:43:19,564] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-06-28 09:43:19,565] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-06-28 09:43:19,565] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-06-28 09:43:19,566] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-06-28 09:43:19,566] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-06-28 09:43:19,566] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-06-28 09:43:19,567] INFO in action_factory: Registered action handler for 'launchApp'
[2025-06-28 09:43:19,568] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-06-28 09:43:19,568] INFO in action_factory: Registered action handler for 'info'
[2025-06-28 09:43:19,568] INFO in action_factory: Registered action handler for 'waitElement'
[2025-06-28 09:43:19,570] INFO in action_factory: Registered action handler for 'compareValue'
[2025-06-28 09:43:19,571] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-06-28 09:43:19,572] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-06-28 09:43:19,572] INFO in action_factory: Registered action handler for 'exists'
[2025-06-28 09:43:19,573] INFO in action_factory: Registered action handler for 'clickElement'
[2025-06-28 09:43:19,574] INFO in action_factory: Registered action handler for 'randomData'
[2025-06-28 09:43:19,574] INFO in action_factory: Registered action handler for 'getValue'
[2025-06-28 09:43:19,575] INFO in action_factory: Registered action handler for 'test'
[2025-06-28 09:43:19,575] INFO in action_factory: Registered action handler for 'restartApp'
[2025-06-28 09:43:19,576] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-06-28 09:43:19,576] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-06-28 09:43:19,576] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'tap': TapAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'wait': WaitAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-06-28 09:43:19,576] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'text': TextAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'info': InfoAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'test': TestAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-06-28 09:43:19,577] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-06-28 09:43:19,577] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-06-28 09:43:19,578] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-06-28 09:43:19,586] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-06-28 09:43:19,591] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-06-28 09:43:19,592] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-28 09:43:19,592] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-06-28 09:43:19,592] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-28 09:43:19,708] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:19,709] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:19,713] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:19,714] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:21,000] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-28 09:43:21,000] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-28 09:43:21,363] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:21] "POST /api/device/connect HTTP/1.1" 200 -
[2025-06-28 09:43:22,391] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-28 09:43:22,391] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-28 09:43:23,609] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-28 09:43:23,609] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-28 09:43:23,610] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:23] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1751067794704_x6nsjqdmr_1751060158987_t2pf44bjt&t=1751067802387 HTTP/1.1" 200 -
[2025-06-28 09:43:24,708] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:24,709] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:24,715] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:24,716] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:27,233] INFO in database: Found 0 execution tracking entries for suite testsuite_execution_20250627_181306
[2025-06-28 09:43:27,234] INFO in database: Found 0 execution tracking entries for suite unknown
[2025-06-28 09:43:27,235] INFO in database: Found 0 execution tracking entries for suite execution_20250627_181306
[2025-06-28 09:43:27,243] INFO in database: Found 0 execution tracking entries for suite testsuite_execution_20250627_181306
[2025-06-28 09:43:27,243] INFO in database: Found 0 execution tracking entries for suite unknown
[2025-06-28 09:43:27,244] INFO in database: Found 0 execution tracking entries for suite execution_20250627_181306
[2025-06-28 09:43:29,707] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:29,708] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:29,712] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:29,715] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:34,706] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:34,707] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:34,710] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:34,712] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:37,960] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:37] "POST /api/reports/export/testsuite_execution_20250627_181306 HTTP/1.1" 200 -
[2025-06-28 09:43:37,966] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:37,967] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:37] "GET /api/reports/download_export/export_testsuite_execution_20250627_181306_20250628_094327.zip?t=1751067817962 HTTP/1.1" 200 -
[2025-06-28 09:43:39,708] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:39,710] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:39,713] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:39,714] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:46,022] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:46,024] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:46] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:46,028] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:46,030] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:46] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:57,967] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:57,968] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:43:57,971] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:43:57,972] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:43:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:08,115] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:08,116] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:08] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:08,119] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:08,120] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:08] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:11,077] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:11,078] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:11] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:11,081] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:11,082] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:11] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:15,265] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:15,266] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:15,269] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:15,270] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:30,264] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:30,265] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:30,268] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:30,270] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:40,374] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:40,376] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:40,379] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:40,380] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:44:50,516] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:44:50,518] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:44:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:02,668] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:02,670] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:04,709] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:04,710] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:05,857] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:05,868] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:05] "GET /api/reports/list HTTP/1.1" 200 -
[2025-06-28 09:45:09,713] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:09,714] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:14,709] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:14,710] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:19,708] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:19,709] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:24,709] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:24,709] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:29,710] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:29,711] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:34,709] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:34,710] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:39,711] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:39,713] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:44,711] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:44,713] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:49,710] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:49,712] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:54,744] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:54,745] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:45:59,712] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:45:59,713] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:45:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:04,710] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:04,711] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:09,712] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:09,713] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:14,710] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:14,711] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:19,711] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:19,712] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:24,710] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:24,711] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:29,709] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:29,710] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:34,710] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:34,711] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:39,712] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:39,713] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:45,263] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:45,264] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:51,372] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250627_181306/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===[2025-06-28 09:46:51,373] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:51] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:46:58,405] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:46:58,406] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:46:58] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:47:09,306] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:47:09,308] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:47:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:47:11,246] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:47:11,247] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:47:11] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:47:15,594] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:47:15,595] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:47:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:47:25,804] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:47:25,805] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:47:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:47:40,241] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:47:40,242] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:47:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:48:09,572] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:48:09,573] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:48:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:49:07,241] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:49:07,243] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:49:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:50:07,244] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:50:07,246] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:50:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:51:07,245] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:51:07,246] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:51:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:52:07,246] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:52:07,247] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:52:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:53:09,545] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:53:09,546] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:53:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:54:07,250] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:54:07,251] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:54:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:55:07,253] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:55:07,254] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:55:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:56:09,583] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:56:09,584] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:56:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:57:09,691] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:57:09,693] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:57:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-28 09:58:09,512] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-28 09:58:09,513] INFO in _internal: 127.0.0.1 - - [28/Jun/2025 09:58:09] "GET /api/reports/latest HTTP/1.1" 200 -
